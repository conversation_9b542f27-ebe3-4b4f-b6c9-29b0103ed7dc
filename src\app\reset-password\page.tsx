'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from 'lucide-react';

const resetPasswordSchema = z.object({
  password: z.string()
    .min(6, 'Lozinka mora imati najmanje 6 karaktera')
    .regex(/[A-Z]/, 'Lozinka mora sadržavati najmanje jedno veliko slovo')
    .regex(/[a-z]/, 'Lozinka mora sadržavati najmanje jedno malo slovo')
    .regex(/[0-9]/, 'Lozinka mora sadržavati najmanje jedan broj')
    .regex(/[^A-Za-z0-9]/, 'Lozinka mora sadržavati najmanje jedan specijalni karakter'),
  confirmPassword: z.string().min(6, 'Potvrdite lozinku'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Lozinke se ne poklapaju',
  path: ['confirmPassword'],
});

type ResetPasswordForm = z.infer<typeof resetPasswordSchema>;

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updatePassword } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isValidSession, setIsValidSession] = useState(false);
  const [isCheckingSession, setIsCheckingSession] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ResetPasswordForm>({
    resolver: zodResolver(resetPasswordSchema),
  });

  // Check if we have a valid password reset session
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Check if we have access_token and refresh_token in URL params
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        
        if (!accessToken || !refreshToken) {
          setIsValidSession(false);
          setIsCheckingSession(false);
          return;
        }

        // Session is valid if we have the tokens
        setIsValidSession(true);
        setIsCheckingSession(false);
      } catch (error) {
        console.error('Error checking session:', error);
        setIsValidSession(false);
        setIsCheckingSession(false);
      }
    };

    checkSession();
  }, [searchParams]);

  const onSubmit = async (data: ResetPasswordForm) => {
    setIsLoading(true);

    try {
      const { error } = await updatePassword(data.password);

      if (error) {
        setError('root', { message: error.message });
        return;
      }

      setIsSuccess(true);
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        router.push('/prijava');
      }, 3000);
    } catch (error) {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingSession) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-4 animate-pulse border border-white/30">
            <Lock className="h-6 w-6 text-white" />
          </div>
          <p className="text-white/80 text-lg">Provjera sesije...</p>
        </div>
      </div>
    );
  }

  if (!isValidSession) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
          <div className="absolute bottom-40 right-10 w-28 h-28 bg-white/10 rounded-full blur-xl"></div>
        </div>

        <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
          <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
            <CardHeader className="text-center">
              <div className="w-16 h-16 bg-red-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-red-500/30">
                <AlertCircle className="h-8 w-8 text-red-400" />
              </div>
              <CardTitle className="text-2xl text-white">Nevažeći link</CardTitle>
              <CardDescription className="text-white/70">
                Link za resetovanje lozinke je nevažeći ili je istekao. Molimo zatražite novi link.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => router.push('/zaboravljena-lozinka')}
                className="w-full bg-white text-[#6700A3] hover:bg-white/90 font-semibold py-3 transition-all duration-200"
              >
                Zatražite novi link
              </Button>
            </CardContent>
          </Card>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#E02F75] via-[#6700A3] to-[#050C38] relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-40 right-10 w-28 h-28 bg-white/10 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <Card className="w-full max-w-md glass-instagram border-white/20 shadow-2xl">
          <CardHeader className="text-center">
            {!isSuccess ? (
              <>
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
                  <Lock className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-2xl text-white">Postavite novu lozinku</CardTitle>
                <CardDescription className="text-white/70">
                  Unesite novu lozinku za svoj nalog
                </CardDescription>
              </>
            ) : (
              <>
                <div className="w-16 h-16 bg-green-500/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-green-500/30">
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
                <CardTitle className="text-2xl text-white">Lozinka je ažurirana!</CardTitle>
                <CardDescription className="text-white/70">
                  Vaša lozinka je uspješno promijenjena. Preusmjeravamo vas na stranicu za prijavu...
                </CardDescription>
              </>
            )}
          </CardHeader>
          
          <CardContent>
            {!isSuccess ? (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                {/* Password */}
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-white">Nova lozinka</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Unesite novu lozinku"
                      {...register('password')}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20 pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="text-red-300 text-sm">{errors.password.message}</p>
                  )}
                </div>

                {/* Confirm Password */}
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-white">Potvrdite lozinku</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Potvrdite novu lozinku"
                      {...register('confirmPassword')}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:ring-white/20 pr-10"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 hover:text-white transition-colors"
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-red-300 text-sm">{errors.confirmPassword.message}</p>
                  )}
                </div>

                {/* Error Message */}
                {errors.root && (
                  <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
                    <p className="text-red-300 text-sm text-center">{errors.root.message}</p>
                  </div>
                )}

                {/* Security Tips */}
                <div className="bg-white/10 border border-white/20 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-white mb-2">Zahtjevi za sigurnu lozinku:</h4>
                  <ul className="text-xs text-white/70 space-y-1">
                    <li>• Najmanje 6 karaktera</li>
                    <li>• Najmanje jedno veliko slovo (A-Z)</li>
                    <li>• Najmanje jedno malo slovo (a-z)</li>
                    <li>• Najmanje jedan broj (0-9)</li>
                    <li>• Najmanje jedan specijalni karakter (!@#$%^&*)</li>
                  </ul>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-white text-[#6700A3] hover:bg-white/90 font-semibold py-3 transition-all duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Ažurira se...' : 'Ažuriraj lozinku'}
                </Button>
              </form>
            ) : (
              <div className="space-y-4">
                <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                  <p className="text-green-300 text-sm text-center">
                    Vaša lozinka je uspješno promijenjena. Možete se sada prijaviti sa novom lozinkom.
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
