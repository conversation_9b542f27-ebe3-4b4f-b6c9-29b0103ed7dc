'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { User } from 'lucide-react';
import { getBusinessCampaignApplications } from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import ApplicationCard from '@/components/campaigns/ApplicationCard';

interface Application {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: number;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
  profiles: {
    id: string;
    username: string;
    full_name: string | null;
    public_display_name: string | null;
    avatar_url: string | null;
  };
}

export default function ApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (!user) return;

    const loadApplications = async () => {
      try {
        const { data, error } = await getBusinessCampaignApplications(user.id);
        if (error) {
          console.error('Error loading applications:', error);
          return;
        }
        setApplications(data || []);
      } catch (error) {
        console.error('Error loading applications:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplications();
  }, [user]);

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const filteredApplications = applications.filter(app => {
    if (activeTab === 'all') return true;
    return app.status === activeTab;
  });

  const stats = {
    total: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    accepted: applications.filter(app => app.status === 'accepted').length,
    rejected: applications.filter(app => app.status === 'rejected').length,
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">
            Aplikacije na kampanje
          </h1>
          <p className="text-muted-foreground mt-1">
            Pregledajte i upravljajte aplikacijama influencera na vaše kampanje
          </p>
        </div>



        {/* Gradient Tabs */}
        <GradientTabs
          tabs={[
            { name: "Sve", value: "all", count: stats.total },
            { name: "Na čekanju", value: "pending", count: stats.pending },
            { name: "Prihvaćeno", value: "accepted", count: stats.accepted },
            { name: "Odbačeno", value: "rejected", count: stats.rejected }
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Applications List */}
        <div className="space-y-4">
          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Nema aplikacija
              </h3>
              <p className="text-gray-600">
                {activeTab === 'all'
                  ? 'Još uvijek nema aplikacija na vaše kampanje.'
                  : `Nema aplikacija sa statusom "${getStatusText(activeTab)}".`}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredApplications.map(application => (
                <ApplicationCard
                  key={application.id}
                  application={application}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
