import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/prijava',
    '/registracija',
    '/zaboravljena-lozinka',
    '/reset-password',
    '/api/auth/callback', // Supabase auth callback
  ]

  // Define protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/marketplace',
    '/campaigns',
    '/influencer',
    '/profil',
    '/chat',
    '/offers',
    '/notifications',
    '/promjena-lozinke'
  ]

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route => {
    if (route === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(route)
  })

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => {
    return pathname.startsWith(route)
  })

  // If it's a public route, allow access
  if (isPublicRoute) {
    return NextResponse.next()
  }

  // If it's a protected route or any other route, check authentication
  if (isProtectedRoute || !isPublicRoute) {

    // Check if user has auth session
    // We'll check for the presence of Supabase auth cookies
    const authCookies = req.cookies.getAll().filter(cookie =>
      cookie.name.includes('sb-') && cookie.name.includes('auth-token')
    )

    // If no auth cookies found, redirect to login
    if (authCookies.length === 0) {
      const redirectUrl = new URL('/prijava', req.url)

      // Add the original URL as a redirect parameter so we can redirect back after login
      if (pathname !== '/prijava') {
        redirectUrl.searchParams.set('redirect', pathname)
      }

      return NextResponse.redirect(redirectUrl)
    }
  }

  // If authenticated or not a protected route, allow access
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
