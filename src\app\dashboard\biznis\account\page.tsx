'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { getProfile, updateProfile } from '@/lib/profiles';
import { Loader2, Save, User, MapPin, CreditCard, Shield } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

const accountSchema = z.object({
  full_name: z.string().min(2, 'Ime mora imati najmanje 2 karaktera'),
  email: z.string().email('Neispravna email adresa'),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
  tax_id: z.string().optional(),
  bank_account: z.string().optional(),
  bank_name: z.string().optional(),
});

type AccountForm = z.infer<typeof accountSchema>;

export default function BusinessAccountPage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<AccountForm>({
    resolver: zodResolver(accountSchema),
  });

  useEffect(() => {
    if (user) {
      loadProfile();
    }
  }, [user]);

  const loadProfile = async () => {
    try {
      setLoading(true);

      const { data, error } = await getProfile(user!.id);
      if (error || !data) {
        toast.error('Greška pri učitavanju profila');
        return;
      }

      setProfile(data);

      // Popuni formu sa postojećim podacima
      reset({
        full_name: data.full_name || '',
        email: user!.email || '',
        phone: data.phone || '',
        address: data.address || '',
        city: data.city || '',
        postal_code: data.postal_code || '',
        country: data.country || '',
        tax_id: data.tax_id || '',
        bank_account: data.bank_account || '',
        bank_name: data.bank_name || '',
      });
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: AccountForm) => {
    if (!user) return;

    setSaving(true);
    try {
      const { error } = await updateProfile(user.id, {
        full_name: data.full_name,
        phone: data.phone || null,
        address: data.address || null,
        city: data.city || null,
        postal_code: data.postal_code || null,
        country: data.country || null,
        tax_id: data.tax_id || null,
        bank_account: data.bank_account || null,
        bank_name: data.bank_name || null,
      });

      if (error) {
        toast.error('Greška pri ažuriranju računa');
        return;
      }

      toast.success('Račun je uspješno ažuriran');
      loadProfile(); // Refresh data
    } catch (err) {
      toast.error('Neočekivana greška');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Moj račun</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Upravljajte svojim privatnim podacima i postavkama računa
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Osnovni podaci */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <User className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Osnovni podaci
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Vaši lični podaci - ove informacije su privatne
              </p>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="full_name" className="text-gray-900 dark:text-gray-100">Puno ime *</Label>
                    <Input
                      id="full_name"
                      {...register('full_name')}
                      placeholder="Marko Marković"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                    {errors.full_name && (
                      <p className="text-sm text-red-600 mt-1">
                        {errors.full_name.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-gray-900 dark:text-gray-100">Email adresa</Label>
                    <Input
                      id="email"
                      type="email"
                      {...register('email')}
                      disabled
                      className="bg-gray-100/60 dark:bg-gray-700/40 border-purple-200/50"
                    />
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      Email se ne može mijenjati
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="phone" className="text-gray-900 dark:text-gray-100">Telefon</Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    placeholder="+387 60 123 456"
                    className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Adresa */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <MapPin className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Adresa
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Vaša adresa za fakturiranje i poslovnu korespondenciju
              </p>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="address" className="text-gray-900 dark:text-gray-100">Ulica i broj</Label>
                  <Input
                    id="address"
                    {...register('address')}
                    placeholder="Zmaja od Bosne 8"
                    className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city" className="text-gray-900 dark:text-gray-100">Grad</Label>
                    <Input
                      id="city"
                      {...register('city')}
                      placeholder="Sarajevo"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="postal_code" className="text-gray-900 dark:text-gray-100">Poštanski broj</Label>
                    <Input
                      id="postal_code"
                      {...register('postal_code')}
                      placeholder="71000"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="country" className="text-gray-900 dark:text-gray-100">Država</Label>
                    <Input
                      id="country"
                      {...register('country')}
                      placeholder="Bosna i Hercegovina"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Finansijski podaci */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <CreditCard className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Finansijski podaci
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Podaci potrebni za fakturiranje i plaćanja
              </p>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="tax_id" className="text-gray-900 dark:text-gray-100">PDV broj / JIB</Label>
                  <Input
                    id="tax_id"
                    {...register('tax_id')}
                    placeholder="*********"
                    className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="bank_account" className="text-gray-900 dark:text-gray-100">Broj računa</Label>
                    <Input
                      id="bank_account"
                      {...register('bank_account')}
                      placeholder="*********0123456"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>

                  <div>
                    <Label htmlFor="bank_name" className="text-gray-900 dark:text-gray-100">Naziv banke</Label>
                    <Input
                      id="bank_name"
                      {...register('bank_name')}
                      placeholder="UniCredit Bank"
                      className="bg-white/60 dark:bg-gray-800/40 border-purple-200/50"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sigurnost */}
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
            <div className="relative p-6">
              <div className="flex items-center gap-2 mb-4">
                <Shield className="h-5 w-5 text-purple-500" />
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  Sigurnost
                </h3>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                Postavke sigurnosti vašeg računa
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Promjena lozinke</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Ažurirajte svoju lozinku redovno radi sigurnosti
                    </p>
                  </div>
                  <Link href="/promjena-lozinke">
                    <Button variant="outline" type="button" className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-gray-200/50">
                      Promijeni lozinku
                    </Button>
                  </Link>
                </div>

                <div className="flex items-center justify-between p-4 bg-white/60 dark:bg-gray-800/40 border border-purple-100/50 dark:border-purple-800/30 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100">Dvofaktorska autentifikacija</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Dodajte dodatni sloj sigurnosti vašem računu
                    </p>
                  </div>
                  <Button variant="outline" type="button" className="bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border-gray-200/50">
                    Podesi 2FA
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className="flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg disabled:opacity-50"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              Sačuvaj promjene
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
