'use client';

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Inbox } from 'lucide-react';
import { getInfluencerOffers, type DirectOfferWithDetails } from '@/lib/offers';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import { GradientTabs } from '@/components/ui/gradient-tabs';
import InfluencerOfferCard from '@/components/campaigns/InfluencerOfferCard';



export default function InfluencerOffersPage() {
  const { user } = useAuth();
  const [offers, setOffers] = useState<DirectOfferWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    if (user) {
      loadOffers();
    }
  }, [user]);



  const loadOffers = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await getInfluencerOffers();
      if (error) {
        console.error('Error loading offers:', error);
      } else {
        setOffers(data || []);
      }
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setIsLoading(false);
    }
  };



  const filteredOffers = offers.filter(offer => {
    if (activeTab === 'all') return true;
    return offer.status === activeTab;
  });






  const stats = {
    total: offers.length,
    pending: offers.filter(o => o.status === 'pending').length,
    accepted: offers.filter(o => o.status === 'accepted').length,
    rejected: offers.filter(o => o.status === 'rejected').length,
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            Direktne ponude
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Upravljajte direktnim ponudama od biznisa
          </p>
        </div>

        {/* Gradient Tabs */}
        <GradientTabs
          tabs={[
            { name: "Sve", value: "all", count: stats.total },
            { name: "Na čekanju", value: "pending", count: stats.pending },
            { name: "Prihvaćeno", value: "accepted", count: stats.accepted },
            { name: "Odbijeno", value: "rejected", count: stats.rejected }
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Offers List */}
        <div className="space-y-4">
          {filteredOffers.length === 0 ? (
            <div className="text-center py-12">
              <Inbox className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'all' ? 'Nema ponuda' : `Nema ponuda sa statusom "${activeTab}"`}
              </h3>
              <p className="text-gray-600 mb-4">
                {activeTab === 'all'
                  ? 'Još niste primili nijednu direktnu ponudu od biznisa.'
                  : 'Nema ponuda sa ovim statusom.'}
              </p>
              <Button asChild className="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 text-white">
                <Link href="/marketplace/campaigns">
                  Pogledaj kampanje
                </Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredOffers.map(offer => (
                <InfluencerOfferCard
                  key={offer.id}
                  offer={offer}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
