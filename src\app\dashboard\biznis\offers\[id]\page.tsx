'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Euro,
  MessageCircle,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Star,
  Building2,
  Users,
  Tag,
} from 'lucide-react';
import { BackButton } from '@/components/ui/back-button';
import { getDirectOffer, type DirectOfferWithDetails } from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import Link from 'next/link';
import { ChatPermissionStatus } from '@/components/chat/ChatPermissionStatus';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import {
  getJobCompletionByDirectOffer,
  type JobCompletion,
} from '@/lib/job-completions';
import { toast } from 'sonner';
import { ApproveJobModal } from '@/components/job-completion/ApproveJobModal';
import { RejectJobModal } from '@/components/job-completion/RejectJobModal';
import { formatDate } from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';

export default function OfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [offer, setOffer] = useState<DirectOfferWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [jobCompletion, setJobCompletion] = useState<JobCompletion | null>(
    null
  );
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadOffer(params.id as string);
      loadJobCompletion(params.id as string);
    }
  }, [params.id]);

  const loadOffer = async (offerId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await getDirectOffer(offerId);
      if (error) {
        console.error('Error loading offer:', error);
        router.push('/dashboard/biznis/offers');
      } else {
        setOffer(data);
      }
    } catch (error) {
      console.error('Error loading offer:', error);
      router.push('/dashboard/biznis/offers');
    } finally {
      setIsLoading(false);
    }
  };

  const loadJobCompletion = async (offerId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data, error } = await getJobCompletionByDirectOffer(offerId);
      if (error) {
        console.error('Error loading job completion:', error);
      } else {
        setJobCompletion(data);
      }
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbijeno';
      case 'completed':
        return 'Završeno';
      case 'cancelled':
        return 'Otkazano';
      default:
        return status;
    }
  };



  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!offer) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="space-y-6">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Ponuda nije pronađena</h2>
            <Link href="/dashboard/biznis/offers">
              <BackButton>
                Nazad na ponude
              </BackButton>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <Link href="/dashboard/biznis/offers">
              <BackButton />
            </Link>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">{offer.title}</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Detalji ponude poslane{' '}
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </p>
          </div>
          <Badge variant="outline" className={`${getStatusColor(offer.status)} font-medium`}>
            <div className="flex items-center space-x-1">
              {getStatusIcon(offer.status)}
              <span>{getStatusText(offer.status)}</span>
            </div>
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Offer Overview */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6 space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Pregled ponude
                  </h3>
                </div>

                <div>
                  <h4 className="font-medium text-lg text-gray-900 dark:text-gray-100 mb-2">
                    {offer.title}
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.description}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                    <div className="flex items-center gap-2">
                      <Euro className="h-4 w-4 text-purple-500" />
                      <span className="text-sm text-gray-600 dark:text-gray-400">Budžet:</span>
                      <span className="font-semibold text-gray-900 dark:text-gray-100">{offer.budget.toLocaleString()} €</span>
                    </div>
                  </div>

                  {offer.deadline && (
                    <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-purple-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">Rok:</span>
                        <span className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(offer.deadline)}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Platforms and Content Types */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">Platforme i tipovi sadržaja</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  Gde influencer treba da objavi sadržaj i koji tip sadržaja
                </p>
                <div className="space-y-3">
                  {offer.platforms.map((platform, platformIndex) => {
                    // Filter content types that belong to this platform
                    const platformContentTypes = offer.content_types.filter(type => {
                      const lowerType = type.toLowerCase();
                      const lowerPlatform = platform.toLowerCase();
                      
                      // First check: exact platform name match in content type
                      if (lowerType.includes(lowerPlatform)) {
                        return true;
                      }
                      
                      // Second check: platform-specific content type mappings
                      // Only match if the content type doesn't already mention another platform
                      const mentionsOtherPlatform = 
                        (lowerPlatform !== 'instagram' && lowerType.includes('instagram')) ||
                        (lowerPlatform !== 'tiktok' && lowerType.includes('tiktok')) ||
                        (lowerPlatform !== 'youtube' && lowerType.includes('youtube')) ||
                        (lowerPlatform !== 'facebook' && lowerType.includes('facebook')) ||
                        (lowerPlatform !== 'twitter' && lowerType.includes('twitter'));
                      
                      if (mentionsOtherPlatform) {
                        return false;
                      }
                      
                      // Platform-specific generic mappings (only if no other platform is mentioned)
                      if (lowerPlatform === 'instagram') {
                        return lowerType.includes('post') || lowerType.includes('story') || lowerType.includes('reel') || lowerType.includes('photo');
                      }
                      if (lowerPlatform === 'tiktok') {
                        return lowerType.includes('video') || lowerType.includes('short');
                      }
                      if (lowerPlatform === 'youtube') {
                        return lowerType.includes('video') || lowerType.includes('short');
                      }
                      if (lowerPlatform === 'facebook') {
                        return lowerType.includes('post') || lowerType.includes('video');
                      }
                      if (lowerPlatform === 'twitter') {
                        return lowerType.includes('tweet') || lowerType.includes('post');
                      }
                      
                      return false;
                    });

                    // Only show platform if it has content types
                    if (platformContentTypes.length === 0) return null;

                    return (
                      <div key={platformIndex} className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-lg">
                            {platform === 'Instagram' ? '📷' :
                             platform === 'TikTok' ? '🎵' :
                             platform === 'YouTube' ? '📺' :
                             platform === 'Facebook' ? '📘' :
                             platform === 'Twitter' ? '🐦' : '📱'}
                          </span>
                          <span className="font-medium text-gray-800 dark:text-gray-200 text-sm">{platform}</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {platformContentTypes.map((type, typeIndex) => (
                            <Badge
                              key={typeIndex}
                              variant="secondary"
                              className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                            >
                              {type === 'post' ? 'Photo Feed Post' :
                               type === 'video' ? 'Video' :
                               type === 'story' ? 'Story' :
                               type === 'reel' ? 'Reel' :
                               type === 'blog' ? 'Blog Post' : type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    );
                  }).filter(Boolean)}
                </div>
              </div>
            </div>

            {/* Requirements */}
            {offer.requirements && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Tag className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Specifični zahtjevi
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.requirements}
                  </p>
                </div>
              </div>
            )}

            {/* Deliverables */}
            {offer.deliverables && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Očekivani rezultati
                    </h3>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.deliverables}
                  </p>
                </div>
              </div>
            )}

            {/* Business Message */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <MessageCircle className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Vaša poruka
                  </h3>
                </div>
                <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {offer.business_message}
                  </p>
                </div>
              </div>
            </div>

            {/* Influencer Response */}
            {offer.influencer_response && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Odgovor influencera
                    </h3>
                  </div>
                  <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-4 border border-purple-100/50 dark:border-purple-800/30">
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {offer.influencer_response}
                    </p>
                  </div>
                  <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                    {offer.status === 'accepted' && offer.accepted_at && (
                      <p>
                        Prihvaćeno{' '}
                        {formatDistanceToNow(new Date(offer.accepted_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && offer.rejected_at && (
                      <p>
                        Odbijeno{' '}
                        {formatDistanceToNow(new Date(offer.rejected_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Influencer Info */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <div className="flex items-center gap-2 mb-4">
                  <User className="h-5 w-5 text-purple-500" />
                  <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Influencer
                  </h3>
                </div>
                
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-16 w-16 border-2 border-white/50">
                    <AvatarImage
                      src={offer.influencer?.avatar_url || ''}
                      alt={getDisplayName(offer.influencer)}
                    />
                    <AvatarFallback className="text-lg bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                      {getInitials(getDisplayName(offer.influencer))}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h4 className="font-semibold text-lg text-gray-900 dark:text-gray-100">
                      {getDisplayName(offer.influencer)}
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      @{offer.influencer?.username}
                    </p>
                  </div>
                </div>

                {offer.influencer.bio && (
                  <p className="text-sm text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                    {offer.influencer.bio}
                  </p>
                )}

                <Link href={`/influencer/${offer.influencer.username}`}>
                  <button className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium bg-white/70 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg transition-all duration-200 hover:shadow-md">
                    <User className="h-4 w-4" />
                    Pogledaj profil
                  </button>
                </Link>
              </div>
            </div>

            {/* Chat Permission Status */}
            {offer.status === 'accepted' && (
              <div className="space-y-4">
                <ChatPermissionStatus
                  businessId={offer.business_id}
                  influencerId={offer.influencer_id}
                  offerId={offer.id}
                  userType="business"
                  onChatEnabled={() => {
                    console.log('Chat enabled for offer:', offer.id);
                  }}
                />

                {/* Chat Button */}
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                  <div className="relative p-4">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                      Komunikacija
                    </h3>
                    <ChatEnableButton
                      businessId={offer.business_id}
                      influencerId={offer.influencer_id}
                      offerId={offer.id}
                      userType="business"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Job Completion Section */}
            {offer.status === 'accepted' && (
              <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
                <div className="relative p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="h-5 w-5 text-purple-500" />
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Završetak posla
                    </h3>
                  </div>
                  
                  {isLoadingJobCompletion ? (
                    <div className="flex items-center justify-center p-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : jobCompletion ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Badge
                          variant={
                            jobCompletion.status === 'submitted'
                              ? 'secondary'
                              : jobCompletion.status === 'approved'
                                ? 'default'
                                : jobCompletion.status === 'rejected'
                                  ? 'destructive'
                                  : 'outline'
                          }
                        >
                          {jobCompletion.status === 'submitted' && 'Poslano na pregled'}
                          {jobCompletion.status === 'approved' && 'Odobreno'}
                          {jobCompletion.status === 'rejected' && 'Odbačeno'}
                          {jobCompletion.status === 'pending' && 'Na čekanju'}
                        </Badge>
                        {jobCompletion.submitted_at && (
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Poslano{' '}
                            {formatDistanceToNow(
                              new Date(jobCompletion.submitted_at),
                              { addSuffix: true, locale: hr }
                            )}
                          </span>
                        )}
                      </div>

                      {jobCompletion.submission_notes && (
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                            Napomene influencera:
                          </h4>
                          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      )}

                      {jobCompletion.submission_files && (
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Prilozi:</h4>
                          <div className="text-sm text-gray-700 dark:text-gray-300">
                            {JSON.parse(jobCompletion.submission_files).length}{' '}
                            fajl(ova) priloženo
                          </div>
                        </div>
                      )}

                      {jobCompletion.status === 'submitted' && (
                        <div className="flex gap-2 pt-4">
                          <button
                            onClick={() => setShowApproveModal(true)}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-green-400 via-emerald-400 to-green-500 hover:from-green-500 hover:via-emerald-500 hover:to-green-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                          >
                            <CheckCircle className="h-4 w-4" />
                            Odobri rad
                          </button>
                          <button
                            onClick={() => setShowRejectModal(true)}
                            className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-400 via-pink-400 to-red-500 hover:from-red-500 hover:via-pink-500 hover:to-red-600 rounded-lg transition-all duration-200 hover:shadow-lg"
                          >
                            <XCircle className="h-4 w-4" />
                            Odbaci rad
                          </button>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
                          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">Vaše napomene:</h4>
                          <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-700 dark:text-gray-300">
                        Influencer još uvijek nije poslao završetak posla
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Timeline */}
            <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60" />
              <div className="relative p-6">
                <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
                  Istorija
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Ponuda poslana</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {formatDistanceToNow(new Date(offer.created_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    </div>
                  </div>

                  {offer.status === 'accepted' && offer.accepted_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Ponuda prihvaćena</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.accepted_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}

                  {offer.status === 'rejected' && offer.rejected_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Ponuda odbijena</p>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {formatDistanceToNow(new Date(offer.rejected_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      {jobCompletion && (
        <>
          <ApproveJobModal
            isOpen={showApproveModal}
            onClose={() => setShowApproveModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowApproveModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.profiles?.full_name ||
              offer?.profiles?.username ||
              'Influencer'
            }
          />

          <RejectJobModal
            isOpen={showRejectModal}
            onClose={() => setShowRejectModal(false)}
            onSuccess={() => {
              loadJobCompletion(params.id as string);
              setShowRejectModal(false);
            }}
            jobCompletionId={jobCompletion.id}
            influencerName={
              offer?.profiles?.full_name ||
              offer?.profiles?.username ||
              'Influencer'
            }
          />
        </>
      )}
    </DashboardLayout>
  );
}
