import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Euro, Eye, Clock, CheckCircle, XCircle, FileText } from 'lucide-react';
import Link from 'next/link';
import { formatDate } from '@/lib/date-utils';

interface Application {
  id: string;
  campaign_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: string;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
}

interface InfluencerApplicationCardProps {
  application: Application;
}

const InfluencerApplicationCard: React.FC<InfluencerApplicationCardProps> = ({ application }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
      {/* Dreamy gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

      <div className="relative p-6 space-y-4">
        {/* Header with campaign title and status */}
        <div className="flex items-start justify-between">
          <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent flex-1 pr-4">
            {application.campaigns.title}
          </h3>
          
          <Badge
            variant="outline"
            className={`${getStatusColor(application.status)} font-medium flex-shrink-0`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(application.status)}
              <span>{getStatusText(application.status)}</span>
            </div>
          </Badge>
        </div>

        {/* Budget info */}
        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Budžet kampanje:
          </p>
          <p className="font-medium text-gray-900 dark:text-gray-100">
            {application.campaigns.budget?.toLocaleString() || 'N/A'} €
          </p>
        </div>

        {/* Application Details */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2 text-sm">
            <Euro className="w-4 h-4 text-purple-500" />
            <span className="text-gray-600 dark:text-gray-400">Vaša cijena:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">{application.proposed_rate} €</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock className="w-4 h-4 text-pink-500" />
            <span className="text-gray-600 dark:text-gray-400">Rok:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">{application.delivery_timeframe}</span>
          </div>
        </div>

        {/* Applied date */}
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="w-4 h-4 text-purple-500" />
          <span className="text-gray-600 dark:text-gray-400">Datum aplikacije:</span>
          <span className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(application.applied_at)}</span>
        </div>

        {/* Proposal preview */}
        {application.proposal_text && (
          <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-4 h-4 text-purple-500" />
              <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Vaš prijedlog:</h4>
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
              {application.proposal_text}
            </p>
          </div>
        )}

        {/* Portfolio links */}
        {application.portfolio_links && application.portfolio_links.length > 0 && (
          <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
            <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">Portfolio linkovi:</h4>
            <div className="flex flex-wrap gap-1">
              {application.portfolio_links.slice(0, 2).map((link, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                >
                  Portfolio {index + 1}
                </Badge>
              ))}
              {application.portfolio_links.length > 2 && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-gradient-to-r from-violet-100 to-purple-100 text-violet-700 border-violet-200"
                >
                  +{application.portfolio_links.length - 2} više
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Action Button */}
        <div className="flex gap-2 pt-2">
          <Link href={`/dashboard/influencer/applications/${application.id}`} className="w-full">
            <button className="flex items-center justify-center gap-2 w-full px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30">
              <Eye className="w-4 h-4" />
              Detaljan pregled
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InfluencerApplicationCard;