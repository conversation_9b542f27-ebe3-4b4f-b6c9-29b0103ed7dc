import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Euro, Eye, Clock, CheckCircle, XCircle, Building2, FileText } from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { formatDate } from '@/lib/date-utils';
import { DirectOfferWithDetails } from '@/lib/offers';

interface InfluencerOfferCardProps {
  offer: DirectOfferWithDetails;
}

const InfluencerOfferCard: React.FC<InfluencerOfferCardProps> = ({ offer }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gradient-to-r from-yellow-100 to-orange-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbijeno';
      case 'completed':
        return 'Završeno';
      case 'cancelled':
        return 'Otkazano';
      default:
        return status;
    }
  };

  return (
    <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-purple-100/80 dark:from-purple-950/20 dark:via-pink-950/10 dark:to-purple-900/30 border border-purple-200/50 dark:border-purple-800/30 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300 group hover:scale-[1.02]">
      {/* Dreamy gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-100/30 via-pink-100/20 to-purple-200/40 dark:from-purple-900/10 dark:via-pink-900/5 dark:to-purple-800/20 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />

      {/* Subtle glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-300/20 via-pink-300/10 to-purple-400/20 dark:from-purple-600/10 dark:via-pink-600/5 dark:to-purple-500/10 blur-xl opacity-50 group-hover:opacity-70 transition-opacity duration-300" />

      <div className="relative p-6 space-y-4">
        {/* Header with business info and status */}
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12 border-2 border-white/50">
              <AvatarImage
                src={offer.businesses.profiles.avatar_url || ''}
                alt={offer.businesses.company_name}
              />
              <AvatarFallback className="bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700">
                <Building2 className="h-6 w-6" />
              </AvatarFallback>
            </Avatar>
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-gray-100">
                {offer.businesses.company_name}
              </h4>
              {offer.businesses.industry && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {offer.businesses.industry}
                </p>
              )}
            </div>
          </div>
          
          <Badge
            variant="outline"
            className={`${getStatusColor(offer.status)} font-medium flex-shrink-0`}
          >
            <div className="flex items-center space-x-1">
              {getStatusIcon(offer.status)}
              <span>{getStatusText(offer.status)}</span>
            </div>
          </Badge>
        </div>

        {/* Offer title */}
        <div>
          <h3 className="text-xl font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            {offer.title}
          </h3>
        </div>

        {/* Description */}
        <div className="bg-white/60 dark:bg-gray-800/40 rounded-lg p-3 border border-purple-100/50 dark:border-purple-800/30">
          <div className="flex items-center gap-2 mb-1">
            <FileText className="w-4 h-4 text-purple-500" />
            <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Opis ponude:</span>
          </div>
          <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2 leading-relaxed">
            {offer.description}
          </p>
        </div>

        {/* Details */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2 text-sm">
            <Euro className="w-4 h-4 text-purple-500" />
            <span className="text-gray-600 dark:text-gray-400">Budžet:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">{offer.budget?.toLocaleString()} €</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="w-4 h-4 text-pink-500" />
            <span className="text-gray-600 dark:text-gray-400">Poslano:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </span>
          </div>
        </div>

        {/* Deadline if exists */}
        {offer.deadline && (
          <div className="flex items-center gap-2 text-sm">
            <Clock className="w-4 h-4 text-red-500" />
            <span className="text-gray-600 dark:text-gray-400">Rok:</span>
            <span className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(offer.deadline)}</span>
          </div>
        )}

        {/* Action Button */}
        <div className="flex gap-2 pt-2">
          <Link href={`/dashboard/influencer/offers/${offer.id}`} className="w-full">
            <button className="flex items-center justify-center gap-2 w-full px-3 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 hover:from-purple-500 hover:via-pink-500 hover:to-purple-600 rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-purple-200/50 dark:hover:shadow-purple-900/30">
              <Eye className="w-4 h-4" />
              Detaljan pregled
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default InfluencerOfferCard;