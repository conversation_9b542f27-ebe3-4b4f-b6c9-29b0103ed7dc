'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, EyeOff, Lock, Shield, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Unesite trenutnu lozinku'),
  newPassword: z.string().min(6, 'Nova lozinka mora imati najmanje 6 karaktera'),
  confirmPassword: z.string().min(6, 'Potvrdite novu lozinku'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Nove lozinke se ne poklapaju',
  path: ['confirmPassword'],
});

type ChangePasswordForm = z.infer<typeof changePasswordSchema>;

export default function PromjenaLozinkePage() {
  const router = useRouter();
  const { updatePassword, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<ChangePasswordForm>({
    resolver: zodResolver(changePasswordSchema),
  });

  const onSubmit = async (data: ChangePasswordForm) => {
    setIsLoading(true);

    try {
      // Update password - Supabase requires user to be authenticated to change password
      const { error } = await updatePassword(data.newPassword);

      if (error) {
        setError('root', { message: error.message });
        return;
      }

      toast.success('Lozinka je uspješno promijenjena!');
      reset();

      // Redirect to dashboard after successful change
      setTimeout(() => {
        router.push('/dashboard');
      }, 2000);
    } catch (error) {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href="/dashboard"
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-[#E02F75] to-[#6700A3] rounded-lg flex items-center justify-center">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Promjena lozinke</h1>
                <p className="text-gray-600">Ažurirajte svoju lozinku za sigurniji pristup</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Card className="shadow-lg border-0">
          <CardHeader className="bg-gradient-to-r from-[#E02F75]/5 to-[#6700A3]/5 border-b">
            <div className="flex items-center gap-3">
              <Lock className="h-5 w-5 text-[#6700A3]" />
              <div>
                <CardTitle className="text-lg">Sigurnosne postavke</CardTitle>
                <CardDescription>
                  Promijenite svoju lozinku da biste održali sigurnost naloga
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="p-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Current Password */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword" className="text-sm font-medium text-gray-700">
                  Trenutna lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showCurrentPassword ? 'text' : 'password'}
                    placeholder="Unesite trenutnu lozinku"
                    {...register('currentPassword')}
                    className="pr-10 focus:ring-[#6700A3] focus:border-[#6700A3]"
                  />
                  <button
                    type="button"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.currentPassword && (
                  <p className="text-red-500 text-sm">{errors.currentPassword.message}</p>
                )}
              </div>

              {/* New Password */}
              <div className="space-y-2">
                <Label htmlFor="newPassword" className="text-sm font-medium text-gray-700">
                  Nova lozinka
                </Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? 'text' : 'password'}
                    placeholder="Unesite novu lozinku"
                    {...register('newPassword')}
                    className="pr-10 focus:ring-[#6700A3] focus:border-[#6700A3]"
                  />
                  <button
                    type="button"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.newPassword && (
                  <p className="text-red-500 text-sm">{errors.newPassword.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  Lozinka mora imati najmanje 6 karaktera
                </p>
              </div>

              {/* Confirm New Password */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                  Potvrdite novu lozinku
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Potvrdite novu lozinku"
                    {...register('confirmPassword')}
                    className="pr-10 focus:ring-[#6700A3] focus:border-[#6700A3]"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-500 text-sm">{errors.confirmPassword.message}</p>
                )}
              </div>

              {/* Error Message */}
              {errors.root && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-600 text-sm">{errors.root.message}</p>
                </div>
              )}

              {/* Security Tips */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Savjeti za sigurnu lozinku:</h4>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• Koristite kombinaciju velikih i malih slova</li>
                  <li>• Dodajte brojeve i specijalne karaktere</li>
                  <li>• Izbjegavajte lične informacije</li>
                  <li>• Ne koristite istu lozinku na više mjesta</li>
                </ul>
              </div>

              {/* Submit Button */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard')}
                  className="flex-1"
                >
                  Otkaži
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 bg-gradient-to-r from-[#E02F75] to-[#6700A3] hover:from-[#E02F75]/90 hover:to-[#6700A3]/90 text-white"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                      Ažurira se...
                    </>
                  ) : (
                    'Promijeni lozinku'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
